"use client";

import { useState, useRef, useEffect } from 'react';

// Define a type for the assessment result for better type checking
interface AssessmentResult {
  accuracyScore?: number;
  fluencyScore?: number;
  completenessScore?: number;
  pronScore?: number;
  prosodyScore?: number;
  detailedResult?: any;
}

const AssessmentScores: React.FC<{ result: AssessmentResult }> = ({ result }) => (
  <div className="player-container">
    <div className="player-label">📊 Assessment Scores</div>
    <div className="scores-grid">
      <div>Accuracy: <span>{result.accuracyScore?.toFixed(1)}</span></div>
      <div>Fluency: <span>{result.fluencyScore?.toFixed(1)}</span></div>
      <div>Completeness: <span>{result.completenessScore?.toFixed(1)}</span></div>
      <div>Pronunciation: <span>{result.pronScore?.toFixed(1)}</span></div>
      <div>Prosody: <span>{result.prosodyScore?.toFixed(1)}</span></div>
    </div>
  </div>
);

export default function Home() {
  const [text, setText] = useState('');
  const [ttsAudioUrl, setTtsAudioUrl] = useState('');
  const [userAudioUrl, setUserAudioUrl] = useState('');
  const [isNavOpen, setIsNavOpen] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordBtnDisabled, setRecordBtnDisabled] = useState(true);
  const [timer, setTimer] = useState('00:00');
  const [assessmentResult, setAssessmentResult] = useState<AssessmentResult | null>(null);

  const mediaRecorder = useRef<MediaRecorder | null>(null);
  const audioChunks = useRef<Blob[]>([]);
  const timerInterval = useRef<NodeJS.Timeout | null>(null);
  const seconds = useRef(0);

  const ttsAudioRef = useRef<HTMLAudioElement>(null);
  const userAudioRef = useRef<HTMLAudioElement>(null);

  const handleGenerateAudio = async () => {
    if (!text.trim()) {
      alert('Please enter some text to generate audio.');
      return;
    }
    try {
      const response = await fetch('/api/tts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text }),
      });
      if (!response.ok) throw new Error('Failed to generate speech.');
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      setTtsAudioUrl(audioUrl);
      setRecordBtnDisabled(false);
      setAssessmentResult(null);
    } catch (error) {
      console.error('Error fetching TTS audio:', error);
      alert('Could not generate audio. Please ensure the backend server is running.');
    }
  };

  // Function to convert audio sample rate from 48kHz to 16kHz
  const convertSampleRate = async (audioBlob: Blob): Promise<Blob> => {
    try {
      console.log('Converting audio sample rate from 48kHz to 16kHz...');
      console.log('Original blob size:', audioBlob.size, 'bytes');

      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioContext = new AudioContext({ sampleRate: 48000 });
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

      console.log('Original audio info:', {
        sampleRate: audioBuffer.sampleRate,
        duration: audioBuffer.duration,
        numberOfChannels: audioBuffer.numberOfChannels,
        length: audioBuffer.length
      });

      // Create offline context with target sample rate of 16kHz
      const targetSampleRate = 16000;
      const targetLength = Math.floor(audioBuffer.duration * targetSampleRate);
      const offlineContext = new OfflineAudioContext(
        audioBuffer.numberOfChannels,
        targetLength,
        targetSampleRate
      );

      // Create buffer source
      const source = offlineContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(offlineContext.destination);
      source.start();

      // Render the audio at 16kHz
      const resampledBuffer = await offlineContext.startRendering();

      console.log('Resampled audio info:', {
        sampleRate: resampledBuffer.sampleRate,
        duration: resampledBuffer.duration,
        numberOfChannels: resampledBuffer.numberOfChannels,
        length: resampledBuffer.length
      });

      // Convert back to WAV blob
      const wavBlob = audioBufferToWav(resampledBuffer);
      console.log('Converted blob size:', wavBlob.size, 'bytes');

      return wavBlob;
    } catch (error) {
      console.error('Error converting sample rate:', error);
      // Return original blob if conversion fails
      return audioBlob;
    }
  };

  // Function to convert AudioBuffer to WAV blob
  const audioBufferToWav = (buffer: AudioBuffer): Blob => {
    const length = buffer.length;
    const numberOfChannels = buffer.numberOfChannels;
    const sampleRate = buffer.sampleRate;
    const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
    const view = new DataView(arrayBuffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * 2, true);
    view.setUint16(32, numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * 2, true);

    // Convert float samples to 16-bit PCM
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
  };

  const assessPronunciation = async (audioBlob: Blob) => {
    try {
      // Convert audio from 48kHz to 16kHz
      const convertedAudioBlob = await convertSampleRate(audioBlob);

      const formData = new FormData();
      formData.append('audio', convertedAudioBlob, 'recording.wav');
      formData.append('text', text);

      const response = await fetch('/api/assessment', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Failed to get assessment.');

      const result = await response.json();
      setAssessmentResult(result);
    } catch (error) {
      console.error('Error assessing pronunciation:', error);
      alert('Could not assess pronunciation. Please ensure the backend server is running.');
    }
  };

  const startRecording = async () => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      alert('Your browser does not support the required audio APIs.');
      return;
    }
    try {
      // Request audio with specific constraints for better speech recognition
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000, // We'll convert this to 16kHz later
          channelCount: 1 // Mono audio for speech
        }
      });

      // Use audio/webm;codecs=opus for better quality if supported, fallback to default
      const options: MediaRecorderOptions = {};
      if (MediaRecorder.isTypeSupported('audio/webm;codecs=opus')) {
        options.mimeType = 'audio/webm;codecs=opus';
      } else if (MediaRecorder.isTypeSupported('audio/mp4')) {
        options.mimeType = 'audio/mp4';
      }

      mediaRecorder.current = new MediaRecorder(stream, options);
      mediaRecorder.current.ondataavailable = (event) => audioChunks.current.push(event.data);
      mediaRecorder.current.onstop = () => {
        const audioBlob = new Blob(audioChunks.current, { type: 'audio/wav' });
        const audioUrl = URL.createObjectURL(audioBlob);
        setUserAudioUrl(audioUrl);
        audioChunks.current = [];
        assessPronunciation(audioBlob);
      };
      mediaRecorder.current.start();
      setIsRecording(true);
    } catch (err) {
      console.error('Error accessing microphone:', err);
      alert('Could not access the microphone. Please check your permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorder.current && mediaRecorder.current.state === 'recording') {
      mediaRecorder.current.stop();
      setIsRecording(false);
    }
  };

  const handleRecordClick = () => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  };

  const startTimer = () => {
    seconds.current = 0;
    setTimer('00:00');
    timerInterval.current = setInterval(() => {
      seconds.current++;
      const minutes = Math.floor(seconds.current / 60).toString().padStart(2, '0');
      const secs = (seconds.current % 60).toString().padStart(2, '0');
      setTimer(`${minutes}:${secs}`);
    }, 1000);
  };

  const stopTimer = () => {
    if(timerInterval.current) {
      clearInterval(timerInterval.current);
    }
  };
  
  useEffect(() => {
    if (isRecording) {
      startTimer();
    } else {
      stopTimer();
    }
  }, [isRecording]);

  return (
    <div className="app-container">
      <header className="app-header">
        <div className="app-logo">Pronunciation Practice</div>
        <button className="menu-btn" onClick={() => setIsNavOpen(!isNavOpen)}>☰</button>
      </header>
      <nav className={`side-nav ${isNavOpen ? 'open' : ''}`}>
        <ul>
          <li><a href="#">Practice</a></li>
          <li><a href="#">Settings</a></li>
          <li><a href="#">Send Feedback</a></li>
          <li><a href="#">Rate the App</a></li>
        </ul>
      </nav>
      <main className="main-content">
        <div className="text-input-container">
          <textarea id="text-input" placeholder="Type text to practice..." value={text} onChange={(e) => setText(e.target.value)}></textarea>
        </div>

        {ttsAudioUrl && (
          <div className="player-container">
            <div className="player-label">🔊 Native Audio</div>
            <div className="player-controls">
              <audio ref={ttsAudioRef} src={ttsAudioUrl} controls />
            </div>
          </div>
        )}

        {userAudioUrl && (
          <div className="player-container">
            <div className="player-label">🎙️ Your Recording</div>
            <div className="player-controls">
              <audio ref={userAudioRef} src={userAudioUrl} controls />
            </div>
          </div>
        )}

        {assessmentResult && <AssessmentScores result={assessmentResult} />}

        <div className="action-buttons">
          <button id="generate-audio-btn" onClick={handleGenerateAudio}>Generate Audio</button>
          <button id="record-btn" className="record-button" disabled={recordBtnDisabled} onClick={handleRecordClick} style={{ backgroundColor: isRecording ? '#28a745' : '#dc3545' }}>
            <span className="mic-icon">{isRecording ? '■' : '🎤'}</span> {isRecording ? 'Stop' : 'Record'}
          </button>
        </div>
        {isRecording && (
          <div id="recording-status">
            <span id="timer">{timer}</span>
          </div>
        )}
      </main>
    </div>
  );
}