// Import the Speech SDK
import * as sdk from "microsoft-cognitiveservices-speech-sdk";
import fs from "fs";
import ffmpeg from "fluent-ffmpeg";
import tmp from "tmp";

// Replace with your own subscription key and service region.
const subscriptionKey: string | undefined = process.env.SPEECH_KEY;
const serviceRegion: string | undefined = process.env.SPEECH_REGION;

if (!subscriptionKey || !serviceRegion) {
  console.error(
    "Please set the SPEECH_SUBSCRIPTION_KEY and SPEECH_SERVICE_REGION environment variables.",
  );
  process.exit(1);
}

// The audio file to analyze
// const audioFile: string = "/Users/<USER>/Downloads/74ea8521-f46b-4460-8e47-2b38767d2a30.wav"
const audioFile: string = "/Users/<USER>/Downloads/2.wav"
// $”1.wav";
// const audioFile = "path/to/your/audio.wav"; // Replace with the path to your audio file

// The reference text for the pronunciation assessment.
// This is the text the user was supposed to say.
const referenceText: string = "My Name is Apple";

function convertTo16k(inputPath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const tempFile = tmp.fileSync({ postfix: ".wav" });
    console.log(`Converting to 16kHz, temp file: ${tempFile.name}`);

    ffmpeg(inputPath)
      .audioCodec("pcm_s16le")
      .audioChannels(1)
      .audioFrequency(16000)
      .toFormat("wav")
      .on("error", (err) => {
        console.error("An error occurred during conversion: " + err.message);
        reject(err);
      })
      .on("end", () => {
        console.log("Conversion finished.");
        resolve(tempFile.name);
      })
      .save(tempFile.name);
  });
}

function createSpeechRecognizer(
  key: string,
  region: string,
  audioFileName: string,
): sdk.SpeechRecognizer {
  const audioConfig = sdk.AudioConfig.fromWavFileInput(
    fs.readFileSync(audioFileName),
  );
  const speechConfig = sdk.SpeechConfig.fromSubscription(key, region);
  // Set the language of the speech to be recognized.
  speechConfig.speechRecognitionLanguage = "en-US";
  return new sdk.SpeechRecognizer(speechConfig, audioConfig);
}

async function performPronunciationAssessment(): Promise<void> {
  let speechRecognizer: sdk.SpeechRecognizer | undefined;
  try {
    const convertedAudioFile = await convertTo16k(audioFile);

    speechRecognizer = createSpeechRecognizer(
      subscriptionKey!,
      serviceRegion!,
      convertedAudioFile,
    );

    const pronunciationAssessmentConfig = new sdk.PronunciationAssessmentConfig(
      referenceText,
      sdk.PronunciationAssessmentGradingSystem.HundredMark,
      sdk.PronunciationAssessmentGranularity.Phoneme,
      true, // Enable miscue calculation
    );

    pronunciationAssessmentConfig.enableProsodyAssessment = true;

    pronunciationAssessmentConfig.applyTo(speechRecognizer);

    console.log("Starting pronunciation assessment...");

    const result: sdk.SpeechRecognitionResult = await new Promise(
      (resolve, reject) => {
        speechRecognizer!.recognizeOnceAsync(resolve, reject);
      },
    );

    if (result.reason === sdk.ResultReason.RecognizedSpeech) {
      console.log(`RECOGNIZED TEXT: ${result.text}`);

      const pronunciationResult: sdk.PronunciationAssessmentResult =
        sdk.PronunciationAssessmentResult.fromResult(result);

      console.log("\n--- Pronunciation Assessment Scores ---");
      console.log(`Accuracy Score: ${pronunciationResult.accuracyScore}`);
      console.log(`Fluency Score: ${pronunciationResult.fluencyScore}`);
      console.log(
        `Completeness Score: ${pronunciationResult.completenessScore}`,
      );
      console.log(`Pronunciation Score: ${pronunciationResult.pronScore}`);
      console.log(`Prosody Score: ${pronunciationResult.prosodyScore}`);

      const resultJson: string | undefined = result.properties.getProperty(
        sdk.PropertyId.SpeechServiceResponse_JsonResult,
      );
      if (resultJson) {
        console.log("\n--- Detailed JSON Result ---");
        console.log(JSON.stringify(JSON.parse(resultJson), null, 4));
      }
    } else if (result.reason === sdk.ResultReason.NoMatch) {
      console.log("NOMATCH: Speech could not be recognized.");
    } else if (result.reason === sdk.ResultReason.Canceled) {
      const cancellation: sdk.CancellationDetails =
        sdk.CancellationDetails.fromResult(result);
      console.log(`CANCELED: Reason=${cancellation.reason}`);

      if (cancellation.reason === sdk.CancellationReason.Error) {
        console.log(`CANCELED: ErrorCode=${cancellation.ErrorCode}`);
        console.log(`CANCELED: ErrorDetails=${cancellation.errorDetails}`);
      }
    }
  } catch (error) {
    console.error("An error occurred:", error);
  } finally {
    if (speechRecognizer) {
      speechRecognizer.close();
    }
  }
}

// Run the assessment.
// Make sure to install the SDK first: npm install microsoft-cognitiveservices-speech-sdk
// And replace placeholders and the audio file path.
// Set environment variables before running:
// export SPEECH_SUBSCRIPTION_KEY="YOUR_KEY"
// export SPEECH_SERVICE_REGION="YOUR_REGION"
performPronunciationAssessment();
