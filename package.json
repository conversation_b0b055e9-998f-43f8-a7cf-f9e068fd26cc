{"name": "speech-service-monorepo", "version": "1.0.0", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "task dev", "build": "task build", "start": "task start", "test": "task test"}, "devDependencies": {"@types/fluent-ffmpeg": "^2.1.27", "@types/tmp": "^0.2.6", "microsoft-cognitiveservices-speech-sdk": "^1.45.0", "typescript": "~5.8.3", "vite-node": "^3.2.4"}, "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/ranglang/speech-service-demo.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/ranglang/speech-service-demo/issues"}, "homepage": "https://github.com/ranglang/speech-service-demo#readme", "description": "", "packageManager": "pnpm@10.15.1+sha512.34e538c329b5553014ca8e8f4535997f96180a1d0f614339357449935350d924e22f8614682191264ec33d1462ac21561aff97f6bb18065351c162c7e8f6de67", "dependencies": {"fluent-ffmpeg": "^2.1.3", "tmp": "^0.2.5"}}